'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';

const UnitConverter = () => {
  const t = useTranslations('converter');
  const [category, setCategory] = useState('length');
  const [fromUnit, setFromUnit] = useState('');
  const [toUnit, setToUnit] = useState('');
  const [inputValue, setInputValue] = useState('');
  const [result, setResult] = useState('');

  const conversions = {
    length: {
      meter: 1,
      kilometer: 0.001,
      centimeter: 100,
      millimeter: 1000,
      inch: 39.3701,
      foot: 3.28084,
      yard: 1.09361,
      mile: 0.000621371
    },
    weight: {
      kilogram: 1,
      gram: 1000,
      pound: 2.20462,
      ounce: 35.274,
      ton: 0.001,
      stone: 0.157473
    },
    temperature: {
      celsius: (c: number) => ({ celsius: c, fahrenheit: c * 9/5 + 32, kelvin: c + 273.15 }),
      fahrenheit: (f: number) => ({ celsius: (f - 32) * 5/9, fahrenheit: f, kelvin: (f - 32) * 5/9 + 273.15 }),
      kelvin: (k: number) => ({ celsius: k - 273.15, fahrenheit: (k - 273.15) * 9/5 + 32, kelvin: k })
    },
    volume: {
      liter: 1,
      milliliter: 1000,
      gallon: 0.264172,
      quart: 1.05669,
      pint: 2.11338,
      cup: 4.22675,
      fluid_ounce: 33.814
    },
    area: {
      square_meter: 1,
      square_kilometer: 0.000001,
      square_centimeter: 10000,
      square_inch: 1550,
      square_foot: 10.7639,
      square_yard: 1.19599,
      acre: 0.000247105,
      hectare: 0.0001
    }
  };

  const unitLabels = {
    length: {
      meter: 'm',
      kilometer: 'km',
      centimeter: 'cm',
      millimeter: 'mm',
      inch: 'in',
      foot: 'ft',
      yard: 'yd',
      mile: 'mi'
    },
    weight: {
      kilogram: 'kg',
      gram: 'g',
      pound: 'lb',
      ounce: 'oz',
      ton: 't',
      stone: 'st'
    },
    temperature: {
      celsius: '°C',
      fahrenheit: '°F',
      kelvin: 'K'
    },
    volume: {
      liter: 'L',
      milliliter: 'mL',
      gallon: 'gal',
      quart: 'qt',
      pint: 'pt',
      cup: 'cup',
      fluid_ounce: 'fl oz'
    },
    area: {
      square_meter: 'm²',
      square_kilometer: 'km²',
      square_centimeter: 'cm²',
      square_inch: 'in²',
      square_foot: 'ft²',
      square_yard: 'yd²',
      acre: 'acre',
      hectare: 'ha'
    }
  };

  const convert = () => {
    const value = parseFloat(inputValue);
    if (isNaN(value) || !fromUnit || !toUnit) {
      setResult('');
      return;
    }

    if (category === 'temperature') {
      const tempConversions = conversions.temperature[fromUnit as keyof typeof conversions.temperature] as any;
      const converted = tempConversions(value);
      setResult(converted[toUnit].toFixed(4));
    } else {
      const categoryConversions = conversions[category as keyof typeof conversions] as any;
      const baseValue = value / categoryConversions[fromUnit];
      const convertedValue = baseValue * categoryConversions[toUnit];
      setResult(convertedValue.toFixed(6));
    }
  };

  const handleCategoryChange = (newCategory: string) => {
    setCategory(newCategory);
    setFromUnit('');
    setToUnit('');
    setInputValue('');
    setResult('');
  };

  const currentUnits = Object.keys(conversions[category as keyof typeof conversions]);

  return (
    <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-6 border border-gray-200 dark:border-gray-700">
      {/* Category Selection */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
          Category
        </label>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
          {Object.keys(conversions).map((cat) => (
            <button
              key={cat}
              onClick={() => handleCategoryChange(cat)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                category === cat
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
              }`}
            >
              {t(cat)}
            </button>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* From Section */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('from')}
          </label>
          <select
            value={fromUnit}
            onChange={(e) => setFromUnit(e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white mb-3"
          >
            <option value="">Select unit</option>
            {currentUnits.map((unit) => (
              <option key={unit} value={unit}>
                {unit.replace('_', ' ')} ({unitLabels[category as keyof typeof unitLabels][unit as keyof typeof unitLabels[keyof typeof unitLabels]]})
              </option>
            ))}
          </select>
          <input
            type="number"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onInput={convert}
            className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            placeholder={t('value')}
          />
        </div>

        {/* To Section */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('to')}
          </label>
          <select
            value={toUnit}
            onChange={(e) => setToUnit(e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white mb-3"
          >
            <option value="">Select unit</option>
            {currentUnits.map((unit) => (
              <option key={unit} value={unit}>
                {unit.replace('_', ' ')} ({unitLabels[category as keyof typeof unitLabels][unit as keyof typeof unitLabels[keyof typeof unitLabels]]})
              </option>
            ))}
          </select>
          <div className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white font-mono text-lg">
            {result || '0'}
          </div>
        </div>
      </div>

      {/* Convert Button */}
      <div className="mt-6 text-center">
        <button
          onClick={convert}
          className="bg-primary text-primary-foreground py-3 px-8 rounded-lg font-semibold hover:bg-primary/90 transition-colors"
        >
          Convert
        </button>
      </div>

      {/* Quick Reference */}
      {result && (
        <div className="mt-6 bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
          <div className="text-center">
            <span className="text-lg font-semibold text-gray-900 dark:text-white">
              {inputValue} {unitLabels[category as keyof typeof unitLabels][fromUnit as keyof typeof unitLabels[keyof typeof unitLabels]]} = {result} {unitLabels[category as keyof typeof unitLabels][toUnit as keyof typeof unitLabels[keyof typeof unitLabels]]}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default UnitConverter;
