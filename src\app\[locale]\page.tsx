import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { 
  Calculator, 
  FlaskConical, 
  DollarSign, 
  ArrowRightLeft, 
  Calendar, 
  Percent 
} from 'lucide-react';

type Props = {
  params: { locale: string };
};

export default function HomePage({ params: { locale } }: Props) {
  const t = useTranslations();

  const calculators = [
    {
      key: 'basic',
      icon: Calculator,
      href: `/${locale}/basic`,
      color: 'bg-blue-500 hover:bg-blue-600',
    },
    {
      key: 'scientific',
      icon: FlaskConical,
      href: `/${locale}/scientific`,
      color: 'bg-purple-500 hover:bg-purple-600',
    },
    {
      key: 'financial',
      icon: DollarSign,
      href: `/${locale}/financial`,
      color: 'bg-green-500 hover:bg-green-600',
    },
    {
      key: 'converter',
      icon: ArrowRightLeft,
      href: `/${locale}/converter`,
      color: 'bg-orange-500 hover:bg-orange-600',
    },
    {
      key: 'date',
      icon: Calendar,
      href: `/${locale}/date`,
      color: 'bg-red-500 hover:bg-red-600',
    },
    {
      key: 'percentage',
      icon: Percent,
      href: `/${locale}/percentage`,
      color: 'bg-indigo-500 hover:bg-indigo-600',
    },
  ];

  return (
    <div className="max-w-6xl mx-auto">
      {/* Hero Section */}
      <div className="text-center mb-12">
        <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-4">
          {t('home.title')}
        </h1>
        <p className="text-xl text-gray-600 dark:text-gray-300 mb-2">
          {t('home.subtitle')}
        </p>
        <p className="text-lg text-gray-500 dark:text-gray-400 max-w-3xl mx-auto">
          {t('home.description')}
        </p>
      </div>

      {/* Calculator Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {calculators.map((calculator) => {
          const Icon = calculator.icon;
          return (
            <Link
              key={calculator.key}
              href={calculator.href}
              className="group block"
            >
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-200 dark:border-gray-700">
                <div className="p-6">
                  <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg text-white mb-4 ${calculator.color} transition-colors`}>
                    <Icon className="h-6 w-6" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                    {t(`${calculator.key}.title`)}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    {t(`${calculator.key}.description`)}
                  </p>
                </div>
                <div className="px-6 pb-6">
                  <div className="flex items-center text-primary font-medium group-hover:text-primary/80 transition-colors">
                    <span>Open Calculator</span>
                    <ArrowRightLeft className="ml-2 h-4 w-4 transform group-hover:translate-x-1 transition-transform" />
                  </div>
                </div>
              </div>
            </Link>
          );
        })}
      </div>

      {/* Features Section */}
      <div className="mt-16 bg-gray-50 dark:bg-gray-800/50 rounded-2xl p-8">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 text-center">
          Why Choose Our Calculator Suite?
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="bg-blue-100 dark:bg-blue-900/30 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <Calculator className="h-8 w-8 text-blue-600 dark:text-blue-400" />
            </div>
            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
              Professional Grade
            </h3>
            <p className="text-gray-600 dark:text-gray-300 text-sm">
              Built with precision and accuracy for professional use
            </p>
          </div>
          <div className="text-center">
            <div className="bg-green-100 dark:bg-green-900/30 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <ArrowRightLeft className="h-8 w-8 text-green-600 dark:text-green-400" />
            </div>
            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
              Multi-Language
            </h3>
            <p className="text-gray-600 dark:text-gray-300 text-sm">
              Available in English, French, Spanish, and Arabic
            </p>
          </div>
          <div className="text-center">
            <div className="bg-purple-100 dark:bg-purple-900/30 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <FlaskConical className="h-8 w-8 text-purple-600 dark:text-purple-400" />
            </div>
            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
              Responsive Design
            </h3>
            <p className="text-gray-600 dark:text-gray-300 text-sm">
              Works perfectly on desktop, tablet, and mobile devices
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
