# Calculator Suite - Professional Multi-Language Calculator Application

A comprehensive Next.js calculator application with full internationalization support, featuring multiple calculator types similar to calculator.net.

## 🌟 Features

### Calculator Types
- **Basic Calculator** - Standard arithmetic operations
- **Scientific Calculator** - Advanced mathematical functions (trigonometry, logarithms, etc.)
- **Financial Calculator** - Loan, mortgage, and investment calculations
- **Unit Converter** - Length, weight, temperature, volume, and area conversions
- **Date Calculator** - Date differences and age calculations
- **Percentage Calculator** - Various percentage calculations

### Internationalization (i18n)
- **4 Languages**: English, French, Spanish, and Arabic
- **RTL Support**: Proper right-to-left layout for Arabic
- **Translated URLs**:
  - English: `/` (default)
  - French: `/fr/`
  - Spanish: `/es/`
  - Arabic: `/ar/`
- **Locale-specific formatting**: Numbers, dates, and currencies

### Design & UX
- **Responsive Design**: Works on desktop, tablet, and mobile
- **Modern UI**: Clean and professional interface
- **Dark Mode Support**: Automatic dark/light theme switching
- **Smooth Animations**: Enhanced user experience with transitions

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd calculator
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Run the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

## 🏗️ Project Structure

```
src/
├── app/
│   ├── [locale]/           # Internationalized routes
│   │   ├── basic/          # Basic calculator
│   │   ├── scientific/     # Scientific calculator
│   │   ├── financial/      # Financial calculator
│   │   ├── converter/      # Unit converter
│   │   ├── date/          # Date calculator
│   │   ├── percentage/    # Percentage calculator
│   │   ├── layout.tsx     # Locale-specific layout
│   │   └── page.tsx       # Home page
│   ├── layout.tsx         # Root layout
│   ├── page.tsx          # Root redirect
│   └── globals.css       # Global styles
├── components/
│   ├── calculators/       # Calculator components
│   ├── Navigation.tsx     # Main navigation
│   └── LanguageSwitcher.tsx # Language selector
├── messages/              # Translation files
│   ├── en.json           # English translations
│   ├── fr.json           # French translations
│   ├── es.json           # Spanish translations
│   └── ar.json           # Arabic translations
├── i18n.ts               # i18n configuration
└── middleware.ts         # Routing middleware
```

## 🌍 Internationalization

The application uses `next-intl` for internationalization with the following features:

- **Automatic locale detection** from URL
- **Fallback to default locale** (English)
- **Type-safe translations** with TypeScript
- **RTL layout support** for Arabic
- **Locale-specific number formatting**

### Adding New Languages

1. Create a new translation file in `messages/[locale].json`
2. Add the locale to `src/i18n.ts`:
```typescript
export const locales = ['en', 'fr', 'es', 'ar', 'new-locale'] as const;
```
3. Update the middleware matcher in `src/middleware.ts`
4. Add the language option to `LanguageSwitcher.tsx`

## 🧮 Calculator Features

### Basic Calculator
- Standard arithmetic operations (+, -, ×, ÷)
- Decimal point support
- Clear and reset functionality
- Error handling

### Scientific Calculator
- Trigonometric functions (sin, cos, tan)
- Logarithmic functions (log, ln)
- Square root and power functions
- Mathematical constants (π, e)
- Factorial calculations
- Degree/Radian mode toggle

### Financial Calculator
- Loan payment calculations
- Interest rate computations
- Total payment and interest calculations
- Currency formatting by locale

### Unit Converter
- **Length**: meter, kilometer, centimeter, inch, foot, yard, mile
- **Weight**: kilogram, gram, pound, ounce, ton, stone
- **Temperature**: Celsius, Fahrenheit, Kelvin
- **Volume**: liter, milliliter, gallon, quart, pint, cup
- **Area**: square meter, square foot, acre, hectare

### Date Calculator
- Date difference calculations
- Age calculator
- Years, months, and days breakdown

### Percentage Calculator
- "What is X% of Y?" calculations
- "X is what % of Y?" calculations
- Percentage increase/decrease calculations

## 🎨 Styling

The application uses:
- **Tailwind CSS** for utility-first styling
- **CSS Custom Properties** for theming
- **Responsive design** with mobile-first approach
- **Dark mode support** with system preference detection

## 🔧 Technologies Used

- **Next.js 15** - React framework with App Router
- **TypeScript** - Type safety
- **next-intl** - Internationalization
- **Tailwind CSS** - Styling
- **Lucide React** - Icons
- **Math.js** - Mathematical calculations
- **date-fns** - Date manipulation

## 📱 Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 🚀 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Deploy automatically

### Other Platforms
The application can be deployed to any platform that supports Next.js:
- Netlify
- AWS Amplify
- Railway
- Render

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- Inspired by calculator.net
- Built with Next.js and modern web technologies
- Icons by Lucide
- Fonts by Google Fonts
