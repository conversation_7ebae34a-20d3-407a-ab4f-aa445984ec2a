'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { evaluate } from 'mathjs';

const BasicCalculator = () => {
  const t = useTranslations('common');
  const [display, setDisplay] = useState('0');
  const [previousValue, setPreviousValue] = useState<string | null>(null);
  const [operation, setOperation] = useState<string | null>(null);
  const [waitingForOperand, setWaitingForOperand] = useState(false);

  const inputNumber = (num: string) => {
    if (waitingForOperand) {
      setDisplay(num);
      setWaitingForOperand(false);
    } else {
      setDisplay(display === '0' ? num : display + num);
    }
  };

  const inputDecimal = () => {
    if (waitingForOperand) {
      setDisplay('0.');
      setWaitingForOperand(false);
    } else if (display.indexOf('.') === -1) {
      setDisplay(display + '.');
    }
  };

  const clear = () => {
    setDisplay('0');
    setPreviousValue(null);
    setOperation(null);
    setWaitingForOperand(false);
  };

  const performOperation = (nextOperation: string) => {
    const inputValue = parseFloat(display);

    if (previousValue === null) {
      setPreviousValue(String(inputValue));
    } else if (operation) {
      const currentValue = previousValue || '0';
      try {
        const result = evaluate(`${currentValue} ${operation} ${inputValue}`);
        setDisplay(String(result));
        setPreviousValue(String(result));
      } catch (error) {
        setDisplay('Error');
        setPreviousValue(null);
        setOperation(null);
        setWaitingForOperand(true);
        return;
      }
    }

    setWaitingForOperand(true);
    setOperation(nextOperation);
  };

  const calculate = () => {
    const inputValue = parseFloat(display);

    if (previousValue !== null && operation) {
      try {
        const result = evaluate(`${previousValue} ${operation} ${inputValue}`);
        setDisplay(String(result));
        setPreviousValue(null);
        setOperation(null);
        setWaitingForOperand(true);
      } catch (error) {
        setDisplay('Error');
        setPreviousValue(null);
        setOperation(null);
        setWaitingForOperand(true);
      }
    }
  };

  const buttons = [
    { label: t('clear'), action: clear, className: 'calculator-button clear col-span-2' },
    { label: '±', action: () => setDisplay(String(-parseFloat(display))), className: 'calculator-button' },
    { label: '÷', action: () => performOperation('/'), className: 'calculator-button operator' },
    
    { label: '7', action: () => inputNumber('7'), className: 'calculator-button' },
    { label: '8', action: () => inputNumber('8'), className: 'calculator-button' },
    { label: '9', action: () => inputNumber('9'), className: 'calculator-button' },
    { label: '×', action: () => performOperation('*'), className: 'calculator-button operator' },
    
    { label: '4', action: () => inputNumber('4'), className: 'calculator-button' },
    { label: '5', action: () => inputNumber('5'), className: 'calculator-button' },
    { label: '6', action: () => inputNumber('6'), className: 'calculator-button' },
    { label: '−', action: () => performOperation('-'), className: 'calculator-button operator' },
    
    { label: '1', action: () => inputNumber('1'), className: 'calculator-button' },
    { label: '2', action: () => inputNumber('2'), className: 'calculator-button' },
    { label: '3', action: () => inputNumber('3'), className: 'calculator-button' },
    { label: '+', action: () => performOperation('+'), className: 'calculator-button operator' },
    
    { label: '0', action: () => inputNumber('0'), className: 'calculator-button col-span-2' },
    { label: '.', action: inputDecimal, className: 'calculator-button' },
    { label: '=', action: calculate, className: 'calculator-button equals' },
  ];

  return (
    <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-6 border border-gray-200 dark:border-gray-700">
      {/* Display */}
      <div className="mb-6">
        <div className="bg-gray-100 dark:bg-gray-900 rounded-lg p-4 text-right">
          <div className="text-3xl font-mono text-gray-900 dark:text-white overflow-hidden">
            {display}
          </div>
          {operation && previousValue && (
            <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              {previousValue} {operation}
            </div>
          )}
        </div>
      </div>

      {/* Button Grid */}
      <div className="grid grid-cols-4 gap-3">
        {buttons.map((button, index) => (
          <button
            key={index}
            onClick={button.action}
            className={`${button.className} h-14 text-lg font-semibold rounded-lg transition-all duration-150 active:scale-95`}
          >
            {button.label}
          </button>
        ))}
      </div>
    </div>
  );
};

export default BasicCalculator;
