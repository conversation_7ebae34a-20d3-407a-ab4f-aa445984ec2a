'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';

const PercentageCalculator = () => {
  const t = useTranslations('percentage');
  
  // What is X% of Y?
  const [percentage1, setPercentage1] = useState('');
  const [value1, setValue1] = useState('');
  const [result1, setResult1] = useState('');

  // X is what % of Y?
  const [value2, setValue2] = useState('');
  const [total2, setTotal2] = useState('');
  const [result2, setResult2] = useState('');

  // Percentage increase/decrease
  const [originalValue, setOriginalValue] = useState('');
  const [newValue, setNewValue] = useState('');
  const [result3, setResult3] = useState('');
  const [isIncrease, setIsIncrease] = useState(true);

  const calculatePercentageOf = () => {
    const percent = parseFloat(percentage1);
    const value = parseFloat(value1);
    
    if (isNaN(percent) || isNaN(value)) {
      setResult1('');
      return;
    }
    
    const result = (percent / 100) * value;
    setResult1(result.toFixed(2));
  };

  const calculateWhatPercent = () => {
    const value = parseFloat(value2);
    const total = parseFloat(total2);
    
    if (isNaN(value) || isNaN(total) || total === 0) {
      setResult2('');
      return;
    }
    
    const result = (value / total) * 100;
    setResult2(result.toFixed(2));
  };

  const calculatePercentageChange = () => {
    const original = parseFloat(originalValue);
    const newVal = parseFloat(newValue);
    
    if (isNaN(original) || isNaN(newVal) || original === 0) {
      setResult3('');
      return;
    }
    
    const change = ((newVal - original) / original) * 100;
    setResult3(Math.abs(change).toFixed(2));
    setIsIncrease(change >= 0);
  };

  const clearAll = () => {
    setPercentage1('');
    setValue1('');
    setResult1('');
    setValue2('');
    setTotal2('');
    setResult2('');
    setOriginalValue('');
    setNewValue('');
    setResult3('');
  };

  return (
    <div className="space-y-8">
      {/* What is X% of Y? */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-6 border border-gray-200 dark:border-gray-700">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
          {t('what_is')} X{t('percent_of')} Y?
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Percentage
            </label>
            <input
              type="number"
              value={percentage1}
              onChange={(e) => setPercentage1(e.target.value)}
              onInput={calculatePercentageOf}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              placeholder="25"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Value
            </label>
            <input
              type="number"
              value={value1}
              onChange={(e) => setValue1(e.target.value)}
              onInput={calculatePercentageOf}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              placeholder="200"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Result
            </label>
            <div className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white font-mono text-lg">
              {result1 || '0'}
            </div>
          </div>
        </div>
      </div>

      {/* X is what % of Y? */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-6 border border-gray-200 dark:border-gray-700">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
          X {t('is_what_percent')} Y?
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Value
            </label>
            <input
              type="number"
              value={value2}
              onChange={(e) => setValue2(e.target.value)}
              onInput={calculateWhatPercent}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              placeholder="50"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Total
            </label>
            <input
              type="number"
              value={total2}
              onChange={(e) => setTotal2(e.target.value)}
              onInput={calculateWhatPercent}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              placeholder="200"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Percentage
            </label>
            <div className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white font-mono text-lg">
              {result2 ? `${result2}%` : '0%'}
            </div>
          </div>
        </div>
      </div>

      {/* Percentage Increase/Decrease */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-6 border border-gray-200 dark:border-gray-700">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
          Percentage Change
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Original Value
            </label>
            <input
              type="number"
              value={originalValue}
              onChange={(e) => setOriginalValue(e.target.value)}
              onInput={calculatePercentageChange}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              placeholder="100"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              New Value
            </label>
            <input
              type="number"
              value={newValue}
              onChange={(e) => setNewValue(e.target.value)}
              onInput={calculatePercentageChange}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              placeholder="120"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Change
            </label>
            <div className={`w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg font-mono text-lg ${
              result3 ? (isIncrease ? 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300' : 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300') : 'bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white'
            }`}>
              {result3 ? `${isIncrease ? '+' : '-'}${result3}%` : '0%'}
            </div>
          </div>
        </div>
        
        {result3 && (
          <div className="mt-4 text-center">
            <span className={`text-lg font-semibold ${isIncrease ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
              {isIncrease ? 'Increase' : 'Decrease'} of {result3}%
            </span>
          </div>
        )}
      </div>

      {/* Clear Button */}
      <div className="text-center">
        <button
          onClick={clearAll}
          className="bg-gray-500 text-white py-3 px-8 rounded-lg font-semibold hover:bg-gray-600 transition-colors"
        >
          Clear All
        </button>
      </div>
    </div>
  );
};

export default PercentageCalculator;
