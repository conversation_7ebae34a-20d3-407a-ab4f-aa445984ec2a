import { useTranslations } from 'next-intl';
import UnitConverter from '@/components/calculators/UnitConverter';

type Props = {
  params: { locale: string };
};

export default function UnitConverterPage({ params: { locale } }: Props) {
  const t = useTranslations('converter');

  return (
    <div className="max-w-4xl mx-auto">
      <div className="text-center mb-8">
        <h1 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
          {t('title')}
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-300">
          {t('description')}
        </p>
      </div>

      <UnitConverter />
    </div>
  );
}
