@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary: #3b82f6;
  --primary-foreground: #ffffff;
  --secondary: #f1f5f9;
  --secondary-foreground: #0f172a;
  --muted: #f8fafc;
  --muted-foreground: #64748b;
  --border: #e2e8f0;
  --input: #ffffff;
  --ring: #3b82f6;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --primary: #3b82f6;
    --primary-foreground: #ffffff;
    --secondary: #1e293b;
    --secondary-foreground: #f1f5f9;
    --muted: #1e293b;
    --muted-foreground: #94a3b8;
    --border: #334155;
    --input: #1e293b;
    --ring: #3b82f6;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* RTL Support */
[dir="rtl"] {
  direction: rtl;
  text-align: right;
}

[dir="rtl"] .rtl\:text-left {
  text-align: left;
}

[dir="rtl"] .rtl\:text-right {
  text-align: right;
}

/* Calculator button styles */
.calculator-button {
  @apply bg-white border border-gray-300 hover:bg-gray-50 active:bg-gray-100 transition-colors duration-150 rounded-lg shadow-sm;
}

.calculator-button.operator {
  @apply bg-blue-500 text-white hover:bg-blue-600 active:bg-blue-700;
}

.calculator-button.equals {
  @apply bg-green-500 text-white hover:bg-green-600 active:bg-green-700;
}

.calculator-button.clear {
  @apply bg-red-500 text-white hover:bg-red-600 active:bg-red-700;
}

/* Dark mode calculator buttons */
@media (prefers-color-scheme: dark) {
  .calculator-button {
    @apply bg-gray-800 border-gray-600 text-white hover:bg-gray-700 active:bg-gray-600;
  }
}
