'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { differenceInDays, differenceInMonths, differenceInYears } from 'date-fns';

const DateCalculator = () => {
  const t = useTranslations('date');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [birthDate, setBirthDate] = useState('');
  const [results, setResults] = useState<{
    days: number;
    months: number;
    years: number;
  } | null>(null);
  const [ageResults, setAgeResults] = useState<{
    days: number;
    months: number;
    years: number;
  } | null>(null);

  const calculateDifference = () => {
    if (!startDate || !endDate) return;

    const start = new Date(startDate);
    const end = new Date(endDate);

    if (start > end) {
      alert('Start date must be before end date');
      return;
    }

    const days = differenceInDays(end, start);
    const months = differenceInMonths(end, start);
    const years = differenceInYears(end, start);

    setResults({ days, months, years });
  };

  const calculateAge = () => {
    if (!birthDate) return;

    const birth = new Date(birthDate);
    const today = new Date();

    if (birth > today) {
      alert('Birth date cannot be in the future');
      return;
    }

    const days = differenceInDays(today, birth);
    const months = differenceInMonths(today, birth);
    const years = differenceInYears(today, birth);

    setAgeResults({ days, months, years });
  };

  const clear = () => {
    setStartDate('');
    setEndDate('');
    setBirthDate('');
    setResults(null);
    setAgeResults(null);
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
      {/* Date Difference Calculator */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-6 border border-gray-200 dark:border-gray-700">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
          {t('calculate_difference')}
        </h2>
        
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('start_date')}
            </label>
            <input
              type="date"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('end_date')}
            </label>
            <input
              type="date"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            />
          </div>

          <button
            onClick={calculateDifference}
            className="w-full bg-primary text-primary-foreground py-3 px-6 rounded-lg font-semibold hover:bg-primary/90 transition-colors"
          >
            {t('calculate_difference')}
          </button>

          {results && (
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-blue-700 dark:text-blue-300 mb-3">
                Date Difference
              </h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-blue-600 dark:text-blue-400">{t('years')}:</span>
                  <span className="font-medium text-blue-700 dark:text-blue-300">
                    {results.years}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-blue-600 dark:text-blue-400">{t('months')}:</span>
                  <span className="font-medium text-blue-700 dark:text-blue-300">
                    {results.months}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-blue-600 dark:text-blue-400">{t('days')}:</span>
                  <span className="font-medium text-blue-700 dark:text-blue-300">
                    {results.days}
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Age Calculator */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-6 border border-gray-200 dark:border-gray-700">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
          {t('calculate_age')}
        </h2>
        
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Birth Date
            </label>
            <input
              type="date"
              value={birthDate}
              onChange={(e) => setBirthDate(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            />
          </div>

          <button
            onClick={calculateAge}
            className="w-full bg-green-500 text-white py-3 px-6 rounded-lg font-semibold hover:bg-green-600 transition-colors"
          >
            {t('calculate_age')}
          </button>

          {ageResults && (
            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-green-700 dark:text-green-300 mb-3">
                Your Age
              </h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-green-600 dark:text-green-400">{t('years')}:</span>
                  <span className="font-medium text-green-700 dark:text-green-300">
                    {ageResults.years}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-green-600 dark:text-green-400">{t('months')}:</span>
                  <span className="font-medium text-green-700 dark:text-green-300">
                    {ageResults.months}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-green-600 dark:text-green-400">{t('days')}:</span>
                  <span className="font-medium text-green-700 dark:text-green-300">
                    {ageResults.days}
                  </span>
                </div>
              </div>
            </div>
          )}

          <div className="text-center">
            <button
              onClick={clear}
              className="bg-gray-500 text-white py-2 px-6 rounded-lg font-semibold hover:bg-gray-600 transition-colors"
            >
              Clear All
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DateCalculator;
