'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { evaluate, pi, e } from 'mathjs';

const ScientificCalculator = () => {
  const t = useTranslations();
  const [display, setDisplay] = useState('0');
  const [expression, setExpression] = useState('');
  const [isRadians, setIsRadians] = useState(true);

  const inputValue = (value: string) => {
    if (display === '0' || display === 'Error') {
      setDisplay(value);
      setExpression(value);
    } else {
      setDisplay(display + value);
      setExpression(expression + value);
    }
  };

  const inputFunction = (func: string) => {
    const newExpression = expression + func + '(';
    setExpression(newExpression);
    setDisplay(display === '0' ? func + '(' : display + func + '(');
  };

  const inputConstant = (constant: string, value: number) => {
    const valueStr = String(value);
    if (display === '0' || display === 'Error') {
      setDisplay(valueStr);
      setExpression(valueStr);
    } else {
      setDisplay(display + valueStr);
      setExpression(expression + valueStr);
    }
  };

  const clear = () => {
    setDisplay('0');
    setExpression('');
  };

  const deleteLast = () => {
    if (display.length > 1) {
      setDisplay(display.slice(0, -1));
      setExpression(expression.slice(0, -1));
    } else {
      setDisplay('0');
      setExpression('');
    }
  };

  const calculate = () => {
    try {
      let expr = expression;
      
      // Convert degrees to radians if needed
      if (!isRadians) {
        expr = expr.replace(/sin\(/g, 'sin(deg(');
        expr = expr.replace(/cos\(/g, 'cos(deg(');
        expr = expr.replace(/tan\(/g, 'tan(deg(');
      }
      
      const result = evaluate(expr);
      setDisplay(String(result));
      setExpression(String(result));
    } catch (error) {
      setDisplay('Error');
      setExpression('');
    }
  };

  const scientificButtons = [
    // Row 1
    { label: t('common.clear'), action: clear, className: 'calculator-button clear', span: 'col-span-2' },
    { label: '⌫', action: deleteLast, className: 'calculator-button' },
    { label: '÷', action: () => inputValue('/'), className: 'calculator-button operator' },
    { label: t('scientific.sin'), action: () => inputFunction('sin'), className: 'calculator-button' },
    { label: t('scientific.cos'), action: () => inputFunction('cos'), className: 'calculator-button' },
    
    // Row 2
    { label: '7', action: () => inputValue('7'), className: 'calculator-button' },
    { label: '8', action: () => inputValue('8'), className: 'calculator-button' },
    { label: '9', action: () => inputValue('9'), className: 'calculator-button' },
    { label: '×', action: () => inputValue('*'), className: 'calculator-button operator' },
    { label: t('scientific.tan'), action: () => inputFunction('tan'), className: 'calculator-button' },
    { label: t('scientific.log'), action: () => inputFunction('log10'), className: 'calculator-button' },
    
    // Row 3
    { label: '4', action: () => inputValue('4'), className: 'calculator-button' },
    { label: '5', action: () => inputValue('5'), className: 'calculator-button' },
    { label: '6', action: () => inputValue('6'), className: 'calculator-button' },
    { label: '−', action: () => inputValue('-'), className: 'calculator-button operator' },
    { label: t('scientific.ln'), action: () => inputFunction('log'), className: 'calculator-button' },
    { label: t('scientific.sqrt'), action: () => inputFunction('sqrt'), className: 'calculator-button' },
    
    // Row 4
    { label: '1', action: () => inputValue('1'), className: 'calculator-button' },
    { label: '2', action: () => inputValue('2'), className: 'calculator-button' },
    { label: '3', action: () => inputValue('3'), className: 'calculator-button' },
    { label: '+', action: () => inputValue('+'), className: 'calculator-button operator' },
    { label: t('scientific.power'), action: () => inputValue('^'), className: 'calculator-button' },
    { label: '!', action: () => inputFunction('factorial'), className: 'calculator-button' },
    
    // Row 5
    { label: '0', action: () => inputValue('0'), className: 'calculator-button', span: 'col-span-2' },
    { label: '.', action: () => inputValue('.'), className: 'calculator-button' },
    { label: '=', action: calculate, className: 'calculator-button equals' },
    { label: t('scientific.pi'), action: () => inputConstant('π', pi), className: 'calculator-button' },
    { label: t('scientific.e'), action: () => inputConstant('e', e), className: 'calculator-button' },
  ];

  return (
    <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-6 border border-gray-200 dark:border-gray-700">
      {/* Display */}
      <div className="mb-6">
        <div className="bg-gray-100 dark:bg-gray-900 rounded-lg p-4">
          <div className="text-right">
            <div className="text-sm text-gray-500 dark:text-gray-400 mb-1 min-h-[20px] font-mono">
              {expression || ' '}
            </div>
            <div className="text-3xl font-mono text-gray-900 dark:text-white overflow-hidden">
              {display}
            </div>
          </div>
        </div>
      </div>

      {/* Mode Toggle */}
      <div className="mb-4 flex justify-center">
        <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-1 flex">
          <button
            onClick={() => setIsRadians(true)}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              isRadians 
                ? 'bg-primary text-primary-foreground' 
                : 'text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
            }`}
          >
            RAD
          </button>
          <button
            onClick={() => setIsRadians(false)}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              !isRadians 
                ? 'bg-primary text-primary-foreground' 
                : 'text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
            }`}
          >
            DEG
          </button>
        </div>
      </div>

      {/* Button Grid */}
      <div className="grid grid-cols-6 gap-3">
        {scientificButtons.map((button, index) => (
          <button
            key={index}
            onClick={button.action}
            className={`${button.className} ${button.span || ''} h-12 text-sm font-semibold rounded-lg transition-all duration-150 active:scale-95`}
          >
            {button.label}
          </button>
        ))}
      </div>
    </div>
  );
};

export default ScientificCalculator;
